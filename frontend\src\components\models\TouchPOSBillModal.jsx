import React, { useRef, useState, useEffect } from "react";
import axios from "axios";
import { useRegister } from "../../context/RegisterContext";
import { useAuth } from "../../context/NewAuthContext";
import logo from "./rs_logo.jpg";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import PrintView from "../print/PrintView";
import { color } from "framer-motion";

const TouchPOSBillModel = ({
  initialProducts = [],
  initialBillDiscount = 0,
  initialTax = 0,
  initialShipping = 0,
  initialTotals = {},
  grandTotal = 0,
  unit = "",
  totalItemDiscount = 0,
  initialCustomerInfo = { name: "", mobile: "", bill_number: "", userId: "" },
  saleType = "Retail",
  onClose,
  paidAmount = 0,
  balanceAmountProp = 0,
  billTime = null,
  paymentType: initialPaymentType = "cash",
  saleId = null,
  isUpdateMode = false,
}) => {
  const { getAuthHeaders } = useRegister();
  const { user } = useAuth();
  const [billNumber, setBillNumber] = useState(initialCustomerInfo.bill_number);
  const printRef = useRef(null);
  const [receivedAmount, setReceivedAmount] = useState(paidAmount);
  const [balanceAmount, setBalanceAmount] = useState(balanceAmountProp);
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(initialCustomerInfo);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showMessagePreview, setShowMessagePreview] = useState(false);
  const [messageContent, setMessageContent] = useState("");
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [paymentType, setPaymentType] = useState(initialPaymentType);
  const [chequeNo, setChequeNo] = useState("");
  const [bankName, setBankName] = useState("");
  const [issueDate, setIssueDate] = useState("");
  const [bankAccounts, setBankAccounts] = useState([]);
  const [selectedBankAccount, setSelectedBankAccount] = useState("");
  const [bankAccountSearch, setBankAccountSearch] = useState("");
  const [showBankAccountDropdown, setShowBankAccountDropdown] = useState(false);
  const [companyDetails, setCompanyDetails] = useState({
      company_name: "FAREES RE BORING CENTER ",
      business_address: "MAIN STREET MALIGAIKADU",
      contact_number: "********** | ********** | **********",
  });
  const [defaultTemplate, setDefaultTemplate] = useState(null);
  const [error, setError] = useState(null);
  const [receivedAmountError, setReceivedAmountError] = useState("");
  const [redeemAmount, setRedeemAmount] = useState(0);

  const [products, setProducts] = useState(() =>
    initialProducts.map((p) => ({
      ...p,
    }))
  );

  const [showAddCustomerForm, setShowAddCustomerForm] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    mobile: "",
    nic_number: "",
    is_credit_customer: false,
  });
  const [customerErrors, setCustomerErrors] = useState({});

  const customerSelectRef = useRef(null);
  const receivedAmountRef = useRef(null);
  const saveButtonRef = useRef(null);
  const printButtonRef = useRef(null);
  const saveOnlyButtonRef = useRef(null);
  const newCustomerNameRef = useRef(null);
  const messageContentRef = useRef(null);
  const chequeNoRef = useRef(null);
  const bankNameRef = useRef(null);
  const issueDateRef = useRef(null);
  const bankAccountRef = useRef(null);

  const defaultPOSTemplate = {
    name: "Default POS",
    type: "POS",
    width: 80,
    height: 297,
    text_elements: [
      "MUNSI TEX",
      "Mosque Building, Police Road, Kalmunai",
      "Mob: **********, **********, **********",
      "Thank You! Visit Again.",
      "System by IMSS",
      "visit🔗: www.imss.lk",
    ],
    text_positions: [
      { x: 10, y: 50 },
      { x: 10, y: 70 },
      { x: 10, y: 80 },
      { x: 10, y: 260 },
      { x: 10, y: 270 },
      { x: 10, y: 280 },
    ],
    text_styles: [
      {
        fontSize: 20,
        color: "#000000",
        fontWeight: "bold",
        textAlign: "center",
        fontFamily: "Cressida, Elephant, cursive",
      },
      {
        fontSize: 13,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 12,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 12,
        color: "#000000",
        fontWeight: "bold",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 8,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 9,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
    ],
    text_sizes: [
      { width: 60, height: 20 },
      { width: 60, height: 15 },
      { width: 60, height: 15 },
      { width: 60, height: 15 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
    ],
    image_elements: [logo],
    image_positions: [{ x: 10, y: 10 }],
    image_sizes: [{ width: 60, height: 40 }],
    placeholder_elements: [
      "c1",
      "t3",
      "t2",
      "t5",
      "t8",
      "t9",
      "t11",
      "t12",
      "t13",
      "t14",
      "t15",
    ],
    placeholder_positions: [
      { x: 10, y: 100 },
      { x: 50, y: 100 },
      { x: 10, y: 110 },
      { x: 50, y: 110 },
      { x: 10, y: 130 },
      { x: 10, y: 200 },
      { x: 10, y: 210 },
      { x: 10, y: 220 },
      { x: 10, y: 230 },
      { x: 10, y: 240 },
      { x: 10, y: 250 },
    ],
    placeholder_sizes: [
      { width: 30, height: 10 },
      { width: 30, height: 10 },
      { width: 30, height: 10 },
      { width: 30, height: 10 },
      { width: 60, height: 50 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 20 },
    ],
    item_list_columns: [
      { id: "no", label: "No", visible: true },
      { id: "name", label: "Name", visible: true },
      { id: "price", label: "Price", visible: true },
      { id: "discount", label: "Dis", visible: true },
      { id: "total", label: "Total", visible: true },
    ],
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const customersResponse = await axios.get(
          "http://127.0.0.1:8000/api/customers",
          getAuthHeaders()
        );
        const customersList = customersResponse.data.data || [];
        setCustomers(customersList);

        if (initialCustomerInfo.id) {
          const existingCustomer = customersList.find(
            (cust) => cust.id == initialCustomerInfo.id
          );
          if (existingCustomer) {
            setSelectedCustomer({
              id: existingCustomer.id,
              name: existingCustomer.customer_name,
              mobile: existingCustomer.phone,
              is_credit_customer: existingCustomer.is_credit_customer,
              bill_number: billNumber,
            });
          }
        }

        try {
          const companyResponse = await axios.get(
            "http://127.0.0.1:8000/api/company-details",
            getAuthHeaders()
          );
          if (companyResponse.data) {
            setCompanyDetails({
              company_name:
                companyResponse.data.company_name ||
                companyDetails.company_name,
              business_address:
                companyResponse.data.business_address ||
                companyDetails.business_address,
              contact_number:
                companyResponse.data.contact_number ||
                companyDetails.contact_number,
            });
          }
        } catch (companyError) {
          console.warn("Using default company details:", companyError.message);
        }

        try {
          const templatesResponse = await axios.get(
            "http://127.0.0.1:8000/api/invoice-templates",
            getAuthHeaders()
          );
          const defaultInvoiceTemplate = templatesResponse.data.data.find(
            (template) => template.is_default === 1
          );
          if (defaultInvoiceTemplate) {
            setDefaultTemplate(defaultInvoiceTemplate);
            setError(null);
          } else {
            console.warn("No default invoice template found.");
            setError(
              "No default invoice template set. Using fallback template."
            );
            setDefaultTemplate(null);
          }
        } catch (templateError) {
          console.error("Invoice template fetch error:", templateError);
          setError(
            `Failed to fetch invoice template: ${templateError.message}. Using fallback template.`
          );
          setDefaultTemplate(null);
        }

        const salesTemplatesResponse = await axios.get(
          "http://127.0.0.1:8000/api/sales-templates",
          getAuthHeaders()
        );
        const defaultSalesTemplate = salesTemplatesResponse.data.data.find(
          (template) => template.is_default
        );
        if (defaultSalesTemplate) {
          setMessageContent(replacePlaceholders(defaultSalesTemplate.content));
        }

        // Fetch bank accounts
        try {
          const bankAccountsResponse = await axios.get(
            "http://127.0.0.1:8000/api/staff-ledger/bank-account-subgroups",
            getAuthHeaders()
          );
          setBankAccounts(bankAccountsResponse.data.data || []);
        } catch (bankError) {
          console.error("Error fetching bank accounts:", bankError);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError(
          `Failed to fetch data: ${error.message}. Using fallback template.`
        );
      }
    };

    fetchData();
  }, [
    getAuthHeaders,
    billNumber,
    initialTotals.finalTotal,
    companyDetails.company_name,
    initialCustomerInfo.id,
  ]);

  useEffect(() => {
    if (customerSelectRef.current) {
      customerSelectRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (showConfirmation && printButtonRef.current) {
      printButtonRef.current.focus();
    }
  }, [showConfirmation]);

  useEffect(() => {
    if (showMessagePreview && messageContentRef.current) {
      messageContentRef.current.focus();
    }
  }, [showMessagePreview]);

  const totals = initialTotals || {};

  const handleCustomerChange = async (e) => {
    const id = e.target.value;
    const customer = customers.find((cust) => cust.id == id);
    let newCustomer;

    if (customer) {
      // Fetch loyalty points for this customer
      try {
        const res = await axios.get("http://127.0.0.1:8000/api/loyalty-points");
        const loyaltyData = res.data.data.find((c) => c.id == id);
        newCustomer = {
          ...customer,
          name: customer.customer_name,
          mobile: customer.phone,
          is_credit_customer: customer.is_credit_customer,
          points_balance: loyaltyData ? loyaltyData.points_balance : 0,
        };
      } catch (err) {
        newCustomer = {
          ...customer,
          name: customer.customer_name,
          mobile: customer.phone,
          is_credit_customer: customer.is_credit_customer,
          points_balance: 0,
        };
      }
    } else {
      newCustomer = {
        name: "Cash Customer",
        mobile: "",
        bill_number: billNumber,
        is_credit_customer: false,
        points_balance: 0,
      };
    }

    setSelectedCustomer(newCustomer);

    // Update message content with new customer info if we have a template
    if (messageContent) {
      try {
        const templatesResponse = await axios.get(
          "http://127.0.0.1:8000/api/sales-templates",
          getAuthHeaders()
        );
        const defaultTemplate = templatesResponse.data.data.find(
          (template) => template.is_default
        );
        if (defaultTemplate) {
          setMessageContent(replacePlaceholders(defaultTemplate.content, newCustomer));
        }
      } catch (error) {
        console.error("Error updating message content:", error);
      }
    }
  };

  const handleReceivedAmountChange = (e) => {
    const amount = parseFloat(e.target.value) || 0;
    setReceivedAmount(amount);
    setBalanceAmount(amount - totals.finalTotal);
  };

  React.useEffect(() => {
    setReceivedAmount(paidAmount);
  }, [paidAmount]);

  // Update balance calculation to include redeemAmount
  useEffect(() => {
    setBalanceAmount((receivedAmount + redeemAmount) - (totals.finalTotal || 0));
  }, [receivedAmount, redeemAmount, totals.finalTotal]);

  const formatCurrency = (amount) => {
    const numAmount = parseFloat(amount || 0);
    if (isNaN(numAmount)) return "LKR 0.00";
    return `LKR ${numAmount.toFixed(2)}`;
  };

  // Function to replace all placeholders in message content
  const replacePlaceholders = (content, customer = selectedCustomer) => {
    if (!content) return "";

    const billUrl = `${window.location.origin}/api/bills/${billNumber}/view`;
    const currentDate = new Date();

    return content
      .replace(/\{\{customer_name\}\}/g, customer?.name || "Cash Customer")
      .replace(/\{\{bill_number\}\}/g, billNumber || "")
      .replace(/\{\{bill_date\}\}/g, currentDate.toLocaleDateString())
      .replace(/\{\{bill_amount\}\}/g, formatCurrency(initialTotals.finalTotal))
      .replace(/\{\{bill_url\}\}/g, billUrl)
      .replace(/\{\{company_name\}\}/g, companyDetails.company_name || "")
      .replace(/\{\{bill_time\}\}/g, currentDate.toLocaleTimeString())
      .replace(/\{\{cashier_name\}\}/g, user?.username || "System");
  };

  const handleSave = async () => {
    setReceivedAmountError("");
    if (redeemAmount > (selectedCustomer?.points_balance || 0)) {
      setReceivedAmountError("Redeem amount cannot exceed available loyalty points.");
      alert("Redeem amount cannot exceed available loyalty points.");
      return;
    }
    if (paymentType === "cheque" && receivedAmount > (totals.finalTotal || grandTotal)) {
      setReceivedAmountError("for cheque payments, received amount cannot be greater than the grand total.");
      alert("For cheque payments, received amount cannot be greater than the grand total.");
      return;
    }

    if (paymentType === "credit") {
      if (!selectedCustomer?.id) {
        alert("Please select a customer for credit sales.");
        return;
      }
    }

    const billData = {
      bill_number: billNumber,
      customer_id: selectedCustomer?.id || null,
      customer_name: selectedCustomer?.name || "Cash Customer",
      subtotal: parseFloat((totals.subTotal || 0).toFixed(2)),
      discount: parseFloat(
        (
          (totals.totalItemDiscounts || 0) +
          (totals.totalSpecialDiscounts || 0) +
          (totals.totalBillDiscount || 0)
        ).toFixed(2)
      ),
      tax: parseFloat((totals.taxAmount || 0).toFixed(2)),
      shipping: parseFloat(initialShipping || 0),
      total: parseFloat((totals.finalTotal || 0).toFixed(2)),
      payment_type: paymentType,
      cheque_no: paymentType === "cheque" ? chequeNo : null,
      bank_name: paymentType === "cheque" ? bankName : null,
      bank: (paymentType === "online" || paymentType === "card" || paymentType === "cheque") ? selectedBankAccount : null,
      issue_date: paymentType === "cheque" ? issueDate : null,
      received_amount: parseFloat(receivedAmount.toFixed(2)),
      balance_amount: parseFloat(
        ((receivedAmount + redeemAmount) - (totals.finalTotal || 0)).toFixed(2)
      ),
      sale_type: saleType,
      items: products.map((product) => ({
        product_id: product.product_id,
        product_name: product.display_name || product.product_name,
        quantity: parseFloat(product.qty),
        mrp: parseFloat(product.mrp || 0),
        unit_price: parseFloat(product.price || 0),
        sales_price: parseFloat(product.sales_price || product.price || 0),
        discount: parseFloat(product.discount || 0),
        special_discount: parseFloat(product.specialDiscount || 0),
        total: parseFloat(product.total || 0),
        supplier: product.supplier || "N/A",
        category: product.category_name || "N/A",
        store_location: product.store_location || "N/A",
        variant_id: product.variant_id || null,
        batch_number: product.batch_number || null,
        expiry_date: product.expiry_date || null,
        free_qty: product.free_qty || 0, // Add the missing free_qty field
      })),
      redeem_amount: redeemAmount,
    };

    try {
      let response;

      if (isUpdateMode && saleId) {
        response = await axios.put(
          `http://127.0.0.1:8000/api/sales/${saleId}`,
          billData,
          getAuthHeaders()
        );
        console.log("Sale updated successfully:", response.data);
      } else {
        response = await axios.post(
          "http://127.0.0.1:8000/api/sales",
          billData,
          getAuthHeaders()
        );
        console.log("Bill saved successfully:", response.data);
      }

      setShowSuccessMessage(true);
      setTimeout(() => {
        setShowSuccessMessage(false);
        onClose(true);
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error(
        isUpdateMode ? "Error updating sale:" : "Error saving bill:",
        error.response?.data || error.message
      );
      alert(
        `Failed to ${isUpdateMode ? "update sale" : "save bill"}: ${error.response?.data?.message || error.message}`
      );
    }
  };

  const handleSendMessage = async () => {
    if (!selectedCustomer?.mobile) {
      alert("Customer mobile number is required to send a message.");
      return;
    }

    try {
      const response = await fetch("http://127.0.0.1:8000/api/send-dialog-sms", {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          ...getAuthHeaders()
        },
        body: JSON.stringify({
          messages: [
            {
              clientRef: `bill_${Date.now()}`,
              number: selectedCustomer.mobile.replace('+', ''),
              mask: "IMSS.lk",
              text: messageContent,
              campaignName: "billNotification"
            }
          ]
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log("Message sent successfully:", data);
      setShowMessagePreview(false);
      setShowConfirmation(true);
    } catch (error) {
      console.error(
        "Error sending message:",
        error.message
      );
      alert(
        `Failed to send message: ${error.message}`
      );
    }
  };

  const renderItemList = (template) => {
    const cleanProductName = (name) => {
      return name.replace(/\s*\[.*\]/, "").trim();
    };

    return (
      <div
        style={{
          fontFamily: "calibri, sans-serif",
          fontSize: "12px",
          color: "#000000",
        }}
      >
        <div
          style={{ display: "flex", marginBottom: "2px", fontWeight: "bold" }}
        >
          <span style={{ width: "30px", textAlign: "center" }}>NO</span>
          <span
            style={{ width: "100px", marginLeft: "5px", textAlign: "left" }}
          >
            ITEM
          </span>
          <span
            style={{ width: "40px", marginLeft: "5px", textAlign: "right" }}
          >
            QTY
          </span>
          <span
            style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
          >
            MRP
          </span>
          <span
            style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
          >
            DISC
          </span>
          <span
            style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
          >
            PRICE
          </span>
          <span
            style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
          >
            TOTAL
          </span>
        </div>
        {products.map((product, index) => {
          const totalDiscount =
            (product.discount || 0) + (product.specialDiscount || 0);
          const price = product.sales_price || product.mrp || 0;
          const total =
            price * (product.qty || 1) - (product.specialDiscount || 0);
          return (
            <div
              key={`item-${index}`}
              style={{ display: "flex", marginBottom: "1px" }}
            >
              <span style={{ width: "30px", textAlign: "center" }}>
                {index + 1}
              </span>
              <span
                style={{
                  width: "100px",
                  marginLeft: "5px",
                  textAlign: "left",
                  wordWrap: "break-word",
                  overflowWrap: "break-word",
                  lineHeight: "1.2",
                }}
              >
                {cleanProductName(product.product_name)}
              </span>
              <span
                style={{ width: "40px", marginLeft: "5px", textAlign: "right" }}
              >
                {product.qty} {product.unit_type || ""}
              </span>
              <span
                style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
              >
                {product.mrp.toFixed(2)}
              </span>
              <span
                style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
              >
                {totalDiscount.toFixed(2)}
              </span>
              <span
                style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
              >
                {price.toFixed(2)}
              </span>
              <span
                style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
              >
                {total.toFixed(2)}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  const renderDynamicTemplate = (template) => {
    const placeholders = {
      c1: selectedCustomer?.name || "Cash Customer",
      t2: new Date().toLocaleDateString(),
      t3: billNumber,
      t5: paymentType,
      t6: companyDetails.company_name,
      t7: companyDetails.contact_number,
      t8: "{{item_list}}",
      t9: totals.subTotalMRP?.toFixed(2) || "0.00",
      t11: totals.totalItemDiscounts?.toFixed(2) || "0.00",
      t12: totals.finalTotal?.toFixed(2) || "0.00",
      t15: receivedAmount.toFixed(2),
      t16: balanceAmount.toFixed(2),
      t17: billTime || new Date().toLocaleTimeString(),
      t18: user?.name || user?.username || "Cashier",
      t19: new Date().toLocaleString(),
      t14: "Exchange is allowed within 7 days with original bill.\ntpw;f;fg;gLk; nghUl;fs; xU thuj;jpw;Fs; khj;jpuk; khw;wpf; nfhLf;fg;gLk;",
    };

    return (
      <div
        style={{
          width: `${template.width}mm`,
          height: `${template.height}mm`,
          position: "relative",
          backgroundColor: "#fff",
          padding: "2mm",
        }}
      >
        {template.text_elements.map((text, index) => (
          <div
            key={`text-${index}`}
            style={{
              position: "absolute",
              left: `${template.text_positions[index].x}px`,
              top: `${template.text_positions[index].y}px`,
              width: `${template.text_sizes[index].width}px`,
              height: `${template.text_sizes[index].height}px`,
              fontSize: `${template.text_styles[index].fontSize}px`,
              color: template.text_styles[index].color,
              fontWeight: template.text_styles[index].fontWeight,
              textAlign: template.text_styles[index].textAlign,
              fontFamily: template.text_styles[index].fontFamily,
              overflow: "hidden",
            }}
          >
            {text}
          </div>
        ))}

        {template.image_elements.map((src, index) => (
          <div
            key={`image-${index}`}
            style={{
              position: "absolute",
              left: `${template.image_positions[index].x}px`,
              top: `${template.image_positions[index].y}px`,
              width: `${template.image_sizes[index].width}px`,
              height: `${template.image_sizes[index].height}px`,
            }}
          >
            <img
              src={src}
              alt={`template-img-${index}`}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "contain",
              }}
            />
          </div>
        ))}

        {template.placeholder_elements.map((key, index) => (
          <div
            key={`placeholder-${index}`}
            style={{
              position: "absolute",
              left: `${template.placeholder_positions[index].x}px`,
              top: `${template.placeholder_positions[index].y}px`,
              width: `${template.placeholder_sizes[index].width}px`,
              height: `${template.placeholder_sizes[index].height}px`,
              fontSize: "12px",
              textAlign: "center",
              fontFamily: "calibri, sans-serif",
              color: "#000000",
            }}
          >
            {key === "t8" ? renderItemList(template) : placeholders[key]}
          </div>
        ))}
      </div>
    );
  };

  const handlePrint = () => {
    const template = defaultTemplate || defaultPOSTemplate;
    const printContent = printRef.current;

    html2canvas(printContent, { scale: 2 }).then((canvas) => {
      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation:
          template.width > template.height ? "landscape" : "portrait",
        unit: "mm",
        format: [template.width, template.height],
      });
      pdf.addImage(imgData, "PNG", 0, 0, template.width, template.height);
      pdf.save(`bill-${billNumber}.pdf`);
    });

    const iframe = document.createElement("iframe");
    iframe.style.position = "absolute";
    iframe.style.width = "0px";
    iframe.style.height = "0px";
    iframe.style.border = "none";
    iframe.style.left = "-1000px";
    document.body.appendChild(iframe);

    const iframeDoc = iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(`
      <html>
        <head>
          <title>Receipt Print</title>
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            body {
              font-family: "calibri", sans-serif;
              font-size: 12px;
              width: 100%;
              margin: 0;
              padding: 2mm;
              color: #000;
              background: white;
            }
            .bill-header {
              margin-bottom: 3px;
              text-align: center;
            }
            .bill-header img {
              max-width: 60mm;
              height: auto;
              margin: 0 auto 3px auto;
            }
            .shop-name {
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 2px;
              font-family: "Cressida", Elephant, cursive;
            }
            .shop-name-tamil {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 2px;
              font-family: "BAMINI-Tamil18", Elephant;
            }
            .shop-address {
              font-size: 13px;
              margin-bottom: 2px;
            }
            .shop-contact {
              font-size: 12px;
              margin-bottom: 3px;
            }
            .bill-info {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 1px;
              font-size: 12px;
              margin-bottom: 3px;
              padding: 0;
            }
            .bill-info div:nth-child(even) {
              text-align: right;
            }
            table {
              width: 100%;
              margin: 5px 0;
              font-size: 12px;
            }
            th, td {
              padding: 1px 0.5px;
            }
            th {
              border-bottom: 1px solid #000;
              border-top: 1px solid #000;
              background-color: #f0f0f0;
              text-align: center;
              font-size: 13px;
            }
            .tr-name td {
              border-bottom: none;
              font-weight: lighter;
              font-size: 14px;
            }
            .tr-details td {
              border-top: none;
              font-size: 13px;
            }
            td:nth-child(1) { width: 8%; text-align: center; }
            td:nth-child(2) { width: 32%; text-align: left; padding-left: 1px; }
            td:nth-child(3) { width: 15%; text-align: right; padding-right: 1px; }
            td:nth-child(4) { width: 15%; text-align: right; padding-right: 1px; }
            td:nth-child(5) { width: 15%; text-align: right; padding-right: 1px; }
            .billing-summary {
              margin-top: 5px;
              font-size: 13px;
              padding: 0 1mm;
            }
            .billing-summary h3 {
              font-size: 12px;
              margin-bottom: 2px;
              text-decoration: underline;
            }
            .billing-summary p {
              margin: 1px 0;
            }
            .billing-summary .grand-total {
              font-size: 13px;
              font-weight: bold;
              margin-top: 2px;
            }
            .terms-conditions {
              font-size: 9px;
              margin-top: 5px;
              padding: 2px 1mm 0 1mm;
              border-top: 1px dashed #000;
            }
            .terms-conditions h4 {
              text-align: center;
              margin-bottom: 1px;
              font-size: 10px;
            }
            .terms-tamil {
              font-family: "Adankappidaari", Adankappidaari;
            }
            .thanks {
              font-size: 12px;
              font-weight: bold;
              margin: 3px 0;
              text-align: center;
            }
            .systemby {
              font-size: 8px;
              text-align: center;
              margin-top: 2px;
            }
            .systemby-web {
              font-size: 9px;
              text-align: center;
              font-style: italic;
              margin-bottom: 2px;
            }
            .text-left { text-align: left; }
            .text-center { text-align: center; }
            .text-right { text-align: right; }
            .font-bold { font-weight: bold; }
          </style>
        </head>
        <body>
          ${printRef.current.innerHTML}
        </body>
      </html>
    `);
    iframeDoc.close();

    iframe.onload = function () {
      setTimeout(() => {
        iframe.contentWindow.focus();
        iframe.contentWindow.print();
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 500);
      }, 100);
    };
  };

  const handleConfirmPrint = () => {
    if (paymentType === "credit") {
      if (!selectedCustomer?.id) {
        alert("Please select a customer for credit sales.");
        return;
      }
    }

    if (paymentType !== "credit" && receivedAmount < totals.finalTotal) {
      alert("Received amount cannot be less than the grand total.");
      return;
    }

    handleSave();
    handlePrint();
  };

  const handleConfirmSave = () => {
    if (paymentType === "credit" || paymentType === "cheque") {
      if (!selectedCustomer?.id) {
        alert("Please select a customer for cheque or credit sales.");
        return;
      }
    }

    setShowConfirmation(false);
    handleSave();
  };

  const handleNewCustomerChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewCustomer({
      ...newCustomer,
      [name]: type === "checkbox" ? checked : value,
    });
    setCustomerErrors({ ...customerErrors, [name]: "" });
  };

  const validateNewCustomer = () => {
    const errors = {};
    if (!newCustomer.name.trim()) {
      errors.name = "Customer name is required";
    }
    if (!newCustomer.mobile.trim()) {
      errors.mobile = "Phone number is required";
    }
    if (!newCustomer.nic_number.trim()) {
      errors.nic_number = "NIC number is required";
    }
    return errors;
  };

  const handleAddCustomer = async (e) => {
    e.preventDefault();
    const validationErrors = validateNewCustomer();
    if (Object.keys(validationErrors).length > 0) {
      setCustomerErrors(validationErrors);
      return;
    }

    try {
      const response = await axios.post(
        "http://127.0.0.1:8000/api/customers",
        {
          customer_name: newCustomer.name,
          phone: newCustomer.mobile,
          nic_number: newCustomer.nic_number,
          is_credit_customer: newCustomer.is_credit_customer,
        },
        getAuthHeaders()
      );

      const customersResponse = await axios.get(
        "http://127.0.0.1:8000/api/customers",
        getAuthHeaders()
      );
      setCustomers(customersResponse.data.data);

      setSelectedCustomer({
        id: response.data.id,
        name: response.data.customer_name,
        mobile: response.data.phone,
        is_credit_customer: response.data.is_credit_customer,
      });

      setNewCustomer({
        name: "",
        mobile: "",
        nic_number: "",
        is_credit_customer: false,
      });
      setShowAddCustomerForm(false);
      setCustomerErrors({});
    } catch (error) {
      console.error("Error adding customer:", error);
      alert(
        `Failed to add customer: ${error.response?.data?.message || error.message}`
      );
    }
  };

  const handleKeyDown = (e) => {
    switch (e.key) {
      case "Enter":
        if (e.target.tagName === "SELECT" && e.target.id === "customerSelect") {
          receivedAmountRef.current?.focus();
        } else if (e.target.id === "receivedAmount") {
          if (!receivedAmount || receivedAmount === 0) {
            setShowMessagePreview(true);
          } else {
            saveButtonRef.current?.focus();
          }
        } else if (e.target.id === "messageContent") {
          return;
        }
        break;
      case "ArrowLeft":
        if (
          showConfirmation &&
          document.activeElement === printButtonRef.current
        ) {
          saveOnlyButtonRef.current?.focus();
        }
        break;
      case "ArrowRight":
        if (
          showConfirmation &&
          document.activeElement === saveOnlyButtonRef.current
        ) {
          printButtonRef.current?.focus();
        }
        break;
      case "Escape":
        if (showConfirmation) {
          setShowConfirmation(false);
        } else if (showMessagePreview) {
          setShowMessagePreview(false);
        } else if (showAddCustomerForm) {
          setShowAddCustomerForm(false);
        }
        break;
      default:
        break;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="relative bg-white rounded-lg shadow-xl w-full max-w-8xl h-[95vh] flex flex-col" onKeyDown={handleKeyDown}>
        <button
          onClick={() => onClose(false)}
          className="absolute p-1 text-gray-500 rounded-full top-2 right-2 hover:bg-gray-100 hover:text-black"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-5 h-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <div className="flex flex-col flex-grow min-h-0 gap-3 p-3 md:flex-row">
          {/* Left Column - Customer Info, Payment Details */}
          <div className="w-full space-y-3 md:w-1/2">
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <label className="block text-xs font-bold text-gray-700">
                  Select Customer
                </label>
                <button
                  onClick={() => {
                    setShowAddCustomerForm(!showAddCustomerForm);
                    if (!showAddCustomerForm && newCustomerNameRef.current) {
                      setTimeout(() => newCustomerNameRef.current.focus(), 100);
                    }
                  }}
                  className="px-2 py-1 text-xs font-medium text-white bg-indigo-600 rounded hover:bg-indigo-700"
                >
                  {showAddCustomerForm ? "Cancel" : "+ Add Customer"}
                </button>
              </div>

              {showAddCustomerForm ? (
                <form
                  onSubmit={handleAddCustomer}
                  className="p-2 space-y-1 rounded bg-gray-50"
                >
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Name
                    </label>
                    <input
                      ref={newCustomerNameRef}
                      type="text"
                      name="name"
                      value={newCustomer.name}
                      onChange={handleNewCustomerChange}
                      className="w-full p-1 mt-1 text-xs border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                    {customerErrors.name && (
                      <p className="mt-1 text-xs text-red-600">
                        {customerErrors.name}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Mobile
                    </label>
                    <input
                      type="tel"
                      name="mobile"
                      value={newCustomer.mobile}
                      onChange={handleNewCustomerChange}
                      className="w-full p-1 mt-1 text-xs border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                    {customerErrors.mobile && (
                      <p className="mt-1 text-xs text-red-600">
                        {customerErrors.mobile}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      NIC Number
                    </label>
                    <input
                      type="text"
                      name="nic_number"
                      value={newCustomer.nic_number}
                      onChange={handleNewCustomerChange}
                      className="w-full p-1 mt-1 text-xs border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                    {customerErrors.nic_number && (
                      <p className="mt-1 text-xs text-red-600">
                        {customerErrors.nic_number}
                      </p>
                    )}
                  </div>
                  <button
                    type="submit"
                    className="w-full px-2 py-1 text-xs font-medium text-white bg-indigo-600 rounded hover:bg-indigo-700"
                  >
                    Save Customer
                  </button>
                </form>
              ) : (
                <select
                  ref={customerSelectRef}
                  id="customerSelect"
                  value={selectedCustomer?.id || ""}
                  onChange={handleCustomerChange}
                  className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Select a customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.customer_name} - {customer.phone}
                    </option>
                  ))}
                </select>
              )}
            </div>

            {/* Remove Customer Information card from here */}

            <div className="p-2 space-y-1 rounded bg-gray-50">
              <h3 className="text-xl font-bold text-gray-800">
                Payment Details
              </h3>

              {/* Payment method buttons moved to right column */}

              <div className="mb-2">
                <div className="mb-1 font-bold text-gray-800 text-l">Total Due :  Rs {(totals.finalTotal || 0).toFixed(2)}</div>
                
                  
                
              </div>

              <div className="space-y-1 text-xs">
                <div className="flex items-center">
                  <label className="w-20 font-bold text-gray-600 text-l">Received</label>
                  <div className="flex items-center flex-1">
                    <span className="mr-1 text-xs">Rs</span>
                    <input
                      id="receivedAmount"
                      ref={receivedAmountRef}
                      type="number"
                      step="0.01"
                      className="flex-1 p-3 text-xl text-right border border-gray-300 rounded"
                      value={receivedAmount}
                      onChange={handleReceivedAmountChange}
                    />
                  </div>
                </div>
                {/* Do NOT show receivedAmountError here for redeem error */}

                <div className="flex items-center mt-2">
                  <label className="w-20 font-bold text-gray-600 text-l">Redeem</label>
                  <div className="flex flex-col flex-1">
                    <div className="flex items-center">
                      <span className="mr-1 text-xs">Rs</span>
                      <input
                        id="redeemAmount"
                        type="number"
                        step="0.01"
                        className="flex-1 p-3 text-xl text-right border border-gray-300 rounded"
                        value={redeemAmount}
                        onChange={e => {
                          const val = Number(e.target.value);
                          setRedeemAmount(val);
                          if (val > (selectedCustomer?.points_balance || 0)) {
                            setReceivedAmountError("Redeem amount cannot exceed available loyalty points.");
                          } else {
                            setReceivedAmountError("");
                          }
                        }}
                        placeholder="Enter amount to redeem from loyalty points"
                        min={0}
                        max={selectedCustomer?.points_balance || 0}
                      />
                    </div>
                    {receivedAmountError && (
                      <p className="self-end mt-1 text-xs text-red-600">{receivedAmountError}</p>
                    )}
                  </div>
                </div>
                {/* <div className="flex items-center">
                  <label className="w-20 font-bold text-gray-600 text-l">Total Paid</label>
                  <span className="flex-1 text-xl text-right">Rs {(receivedAmount + redeemAmount).toFixed(2)}</span>
                </div> */}
                <div className="flex items-center">
                  <label className="w-20 font-bold text-gray-600 text-l">Balance</label>
                  <span className="mr-1 text-xs">Rs</span>
                  <input
                    type="text"
                    className="flex-1 p-3 text-xl text-right border border-gray-300 rounded bg-gray-50"
                    value={balanceAmount.toFixed(2)}
                    readOnly
                  />
                  
                </div>

                <div className="flex items-center">
                  <label className="w-20 font-bold text-gray-600 text-l">Return</label>
                  <div className="flex items-center flex-1">
                    <span className="mr-1 text-xs">Rs</span>
                    <input
                      type="text"
                      className="flex-1 p-3 text-xl text-right border border-gray-300 rounded bg-gray-50"
                      value={
                        balanceAmount > 0 ? balanceAmount.toFixed(2) : "0.00"
                      }
                      readOnly
                    />
                  </div>
                </div>
              </div>

              {/* <div className="mt-2">
                <label className="block mb-1 text-xs font-medium text-gray-700">
                  Quick Amount
                </label>
                <div className="grid grid-cols-4 gap-2">
                  {[5, 10, 20, 50, 100, 500, 1000, 5000, "Exact"].map((amount, idx) => (
                    <button
                      key={amount}
                      type="button"
                      onClick={() => {
                        if (amount === "Exact") {
                          setReceivedAmount(totals.finalTotal || 0);
                          setBalanceAmount(0);
                        } else {
                          setReceivedAmount(amount);
                          setBalanceAmount(amount - (totals.finalTotal || 0));
                        }
                      }}
                      className={
                        amount === "Exact"
                          ? "bg-orange-600 hover:bg-orange-700 h-8 text-xs font-bold text-white rounded transition-all duration-150 shadow-sm"
                          : idx < 4
                          ? "bg-blue-600 hover:bg-blue-700 h-8 text-xs font-bold text-white rounded transition-all duration-150 shadow-sm"
                          : idx < 8
                          ? "bg-green-600 hover:bg-green-700 h-8 text-xs font-bold text-white rounded transition-all duration-150 shadow-sm"
                          : "bg-gray-600 hover:bg-gray-700 h-8 text-xs font-bold text-white rounded transition-all duration-150 shadow-sm"
                      }
                    >
                      {amount === "Exact" ? "Exact" : `${amount}`}
                    </button>
                  ))}
                </div>
              </div> */}
                {/* Quick Amount Buttons */}
                <div className="mt-4">
                    <label className="block mb-3 text-sm font-medium text-gray-700">
                      Quick Amount
                    </label>
                    <div className="grid grid-cols-4 gap-3">
                      {[5, 10, 20, 50, 100, 500, 1000,5000, "Exact"].map(
                        (amount, index) => (
                          <button
                            key={amount}
                            type="button"
                            onClick={() => {
                              if (amount === "Exact") {
                                setReceivedAmount(totals.finalTotal || 0);
                                setBalanceAmount(0);
                              } else {
                                setReceivedAmount(amount);
                                setBalanceAmount(amount - (totals.finalTotal || 0));
                              }
                            }}
                            className={`h-12 text-sm font-bold text-white rounded-lg shadow-md active:scale-95 transform transition-all duration-150 hover:shadow-lg ${
                              amount === "Exact"
                                ? "bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 border-2 border-orange-300"
                                : index < 4
                                  ? "bg-gradient-to-br from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 border-2 border-teal-300"
                                  : "bg-gradient-to-br from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 border-2 border-indigo-300"
                            }`}
                          >
                            {amount === "Exact" ? "Exact" : `${amount}`}
                          </button>
                        )
                      )}
                    </div>
                  
                </div>
            </div>
          </div>

          {/* Right Column - Billing Summary, Keypad */}
          <div className="w-full space-y-3 md:w-1/2">
            {/* Customer Information and Billing Summary side by side */}
            <div className="flex flex-col gap-3 md:flex-row">
              <div className="w-full p-2 space-y-1 rounded bg-gray-50 md:w-1/2">
                <h2 className="font-bold text-gray-800 text-md">
                  Customer Information
                </h2>
                <div className="space-y-1 text-xs font-bold">
                  <p>
                    <span className="font-medium">Name:</span>{" "}
                    {selectedCustomer?.name || "Cash Customer"}
                  </p>
                  <p>
                    <span className="font-medium">Mobile:</span>{" "}
                    {selectedCustomer?.mobile || "N/A"}
                  </p>
                  <p>
                    <span className="font-medium">Loyalty Points:</span>{" "}
                    {selectedCustomer?.points_balance != null ? selectedCustomer.points_balance : "-"}
                  </p>
                  <p>
                    <span className="font-medium">Date:</span>{" "}
                    {new Date().toLocaleDateString()}
                  </p>
                  <p>
                    <span className="font-medium">Bill No:</span> {billNumber}
                  </p>
                </div>
              </div>
              <div className="w-full p-2 space-y-1 rounded bg-gray-50 md:w-1/2">
                <h2 className="font-bold text-gray-800 text-md">
                  Billing Summary
                </h2>
                <div className="pt-2 mt-5 border-t text-l">
                  <p>
                    <strong>Subtotal (MRP):</strong>{" "}
                    {formatCurrency(totals.subTotalMRP)}
                  </p>
                  <p>
                    <strong>Item Discounts:</strong>{" "}
                    {formatCurrency(totals.totalItemDiscounts)}
                  </p>
                  <p className="text-xl font-bold">
                    <strong>Grand Total:</strong>{" "}
                    {formatCurrency(totals.finalTotal)}
                  </p>
                  {/* Free Qty Summary */}
                  <p>
                    <strong>Total Free Qty:</strong>{" "}
                    {products && products.length > 0
                      ? products.reduce((sum, p) => sum + (p.free_qty || 0), 0)
                      : 0}
                  </p>
                </div>
              </div>
            </div>
            {/* Payment Method Buttons - moved here */}
            <div className="p-2 space-y-1 rounded bg-gray-50">
              <h3 className="text-sm font-semibold text-gray-800">
                Payment Method
              </h3>
              {/* <div className="flex flex-wrap gap-5">
                {["CASH", "CARD", "ONLINE", "CHEQUE", "CREDIT"].map((type) => (
                  <button
                    key={type}
                    type="button"
                    onClick={() => setPaymentType(type.toLowerCase())}
                    className={`px-3 py-1 text-xs font-bold rounded transition-all duration-200 ${
                      paymentType === type.toLowerCase()
                        ? "bg-blue-500 text-white border border-blue-300"
                        : "bg-gray-100 text-gray-700 border border-gray-300 hover:bg-blue-400 hover:text-white"
                    }`}
                  >
                    {type}
                  </button>
                ))}
              </div> */}
              
              {/* Payment Type Buttons */}
              <div className="flex flex-wrap gap-3">
                {["CASH", "CARD", "ONLINE", "CHEQUE", "CREDIT"].map((type) => (
                  <button
                    key={type}
                    type="button"
                    onClick={() => setPaymentType(type.toLowerCase())}
                    className={`px-6 py-3 text-sm font-bold rounded-lg shadow-md transform transition-all duration-200 active:scale-95 ${
                      paymentType === type.toLowerCase()
                        ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white border-2 border-blue-300 hover:from-blue-600 hover:to-blue-700 hover:shadow-lg hover:scale-105"
                        : "bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 border-2 border-gray-300 hover:from-blue-400 hover:to-blue-500 hover:text-white hover:border-blue-300 hover:shadow-lg hover:scale-105"
                    }`}
                  >
                    {type}
                  </button>
                ))}
              </div>

              {/* Cheque Payment Fields */}
              {paymentType === "cheque" && (
                <div className="p-4 space-y-3 border-2 border-blue-200 rounded-lg bg-blue-50">
                  <h4 className="text-lg font-semibold text-center text-blue-800">Cheque Details</h4>
                  
                  <div className="grid grid-cols-1 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-blue-700">
                        Cheque No
                      </label>
                      <input
                        ref={chequeNoRef}
                        type="text"
                        value={chequeNo}
                        onChange={(e) => setChequeNo(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            bankNameRef.current?.focus();
                          }
                        }}
                        className="w-full p-3 text-sm border-2 border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter cheque number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-blue-700">
                        Bank Name
                      </label>
                      <input
                        ref={bankNameRef}
                        type="text"
                        value={bankName}
                        onChange={(e) => setBankName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            issueDateRef.current?.focus();
                          }
                        }}
                        className="w-full p-3 text-sm border-2 border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter bank name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-blue-700">
                        Issue Date
                      </label>
                      <input
                        ref={issueDateRef}
                        type="date"
                        value={issueDate}
                        onChange={(e) => setIssueDate(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            receivedAmountRef.current?.focus();
                          }
                        }}
                        className="w-full p-3 text-sm border-2 border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Bank Account Selection for Online, Card, and Cheque */}
              {(paymentType === "online" || paymentType === "card" || paymentType === "cheque") && (
                <div className="p-4 space-y-3 border-2 border-green-200 rounded-lg bg-green-50">
                  <h4 className="text-lg font-semibold text-center text-green-800">Bank Account</h4>
                  
                  <div className="relative">
                    <label className="block text-sm font-medium text-green-700">
                      Select Bank Account
                    </label>
                    <input
                      ref={bankAccountRef}
                      type="text"
                      value={bankAccountSearch}
                      onChange={(e) => {
                        setBankAccountSearch(e.target.value);
                        setShowBankAccountDropdown(true);
                      }}
                      onFocus={() => setShowBankAccountDropdown(true)}
                      onBlur={() => setTimeout(() => setShowBankAccountDropdown(false), 200)}
                      className="w-full p-3 text-sm border-2 border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="Search bank account..."
                    />
                    {showBankAccountDropdown && (
                      <div className="absolute z-50 w-full mt-1 overflow-y-auto bg-white border-2 border-green-300 rounded-lg shadow-lg max-h-48">
                        {bankAccounts
                          .filter(account => 
                            account.name.toLowerCase().includes(bankAccountSearch.toLowerCase())
                          )
                          .map((account) => (
                            <div
                              key={`${account.type}-${account.id}`}
                              className="px-3 py-2 text-sm cursor-pointer hover:bg-green-50"
                              onMouseDown={() => {
                                setSelectedBankAccount(`${account.type}-${account.id}`);
                                setBankAccountSearch(account.name);
                                setShowBankAccountDropdown(false);
                              }}
                            >
                              <div className="font-medium">{account.name}</div>
                              <div className="text-xs text-gray-500">{account.account_group}</div>
                            </div>
                          ))}
                        {bankAccounts.filter(account => 
                          account.name.toLowerCase().includes(bankAccountSearch.toLowerCase())
                        ).length === 0 && (
                          <div className="px-3 py-2 text-sm text-gray-500">
                            No bank accounts found
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            {/* Number Keypad
            <div className="grid grid-cols-3 gap-2 mt-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, "Clear", 0, "."].map((num, idx) => (
                <button
                  key={num}
                  onClick={() => {
                    if (num === "Clear") {
                      setReceivedAmount(0);
                      setBalanceAmount(-(totals.finalTotal || 0));
                    } else if (num === ".") {
                      setReceivedAmount((prev) => prev.toString().includes(".") ? prev : prev + ".");
                    } else {
                      setReceivedAmount((prev) => prev === 0 ? num : parseFloat(prev.toString() + num.toString()));
                      setBalanceAmount((prev) => {
                        const newValue = (prev === 0 ? num : parseFloat(prev.toString() + num.toString()));
                        return newValue - (totals.finalTotal || 0);
                      });
                    }
                  }}
                  className={
                    num === "Clear"
                      ? "text-xs font-bold text-white h-8 rounded bg-gradient-to-br from-blue-500 to-blue-600 hover:bg-red-700 shadow-sm transition-all duration-150"
                      : num === 0
                      ? "text-sm font-bold text-white h-8 rounded bg-purple-600 hover:bg-purple-700 shadow-sm transition-all duration-150"
                      : num === "."
                      ? "text-sm font-bold text-white h-8 rounded bg-cyan-600 hover:bg-cyan-700 shadow-sm transition-all duration-150"
                      : idx < 3
                      ? "text-sm font-bold text-white h-8 rounded bg-blue-600 hover:bg-blue-700 shadow-sm transition-all duration-150"
                      : idx < 6
                      ? "text-sm font-bold text-white h-8 rounded bg-green-600 hover:bg-green-700 shadow-sm transition-all duration-150"
                      : idx < 9
                      ? "text-sm font-bold text-white h-8 rounded bg-yellow-600 hover:bg-yellow-700 shadow-sm transition-all duration-150"
                      : "text-sm font-bold text-white h-8 rounded bg-gray-600 hover:bg-gray-700 shadow-sm transition-all duration-150"
                  }
                >
                  {num}
                </button>
              ))}
            </div> */}
            {/* Number Keypad */}
            <div className="p-4 space-y-3 rounded-md bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-800">
                Number Keypad
              </h3>
              <div className="grid grid-cols-3 gap-2">
                {/* Numbers 1-9 */}
                {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
                  <button
                    key={num}
                    type="button"
                    onClick={() => {
                      const currentValue = receivedAmount.toString();
                      const newValue =
                        currentValue === "0"
                          ? num.toString()
                          : currentValue + num.toString();
                      const numValue = parseFloat(newValue);
                      setReceivedAmount(numValue);
                      setBalanceAmount(numValue - (totals.finalTotal || 0));
                    }}
                    className="text-xl font-bold text-white transition-all duration-200 transform border-2 border-blue-300 shadow-lg h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl hover:from-blue-600 hover:to-blue-700 active:scale-95 hover:shadow-xl"
                  >
                    {num}
                  </button>
                ))}

                {/* Bottom row: Clear, 0, Decimal */}
                <button
                  type="button"
                  onClick={() => {
                    setReceivedAmount(0);
                    setBalanceAmount(-(totals.finalTotal || 0));
                  }}
                  className="text-lg font-bold text-white transition-all duration-200 transform border-2 border-red-300 shadow-lg h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-xl hover:from-red-600 hover:to-red-700 active:scale-95 hover:shadow-xl"
                >
                  Clear
                </button>

                <button
                  type="button"
                  onClick={() => {
                    const currentValue = receivedAmount.toString();
                    const newValue =
                      currentValue === "0" ? "0" : currentValue + "0";
                    const numValue = parseFloat(newValue);
                    setReceivedAmount(numValue);
                    setBalanceAmount(numValue - (totals.finalTotal || 0));
                  }}
                  className="text-xl font-bold text-white transition-all duration-200 transform border-2 border-blue-300 shadow-lg h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl hover:from-blue-600 hover:to-blue-700 active:scale-95 hover:shadow-xl"
                >
                  0
                </button>

                <button
                  type="button"
                  onClick={() => {
                    const currentValue = receivedAmount.toString();
                    if (!currentValue.includes(".")) {
                      const newValue = currentValue + ".";
                      setReceivedAmount(parseFloat(newValue) || 0);
                    }
                  }}
                  className="text-2xl font-bold text-white transition-all duration-200 transform border-2 border-purple-300 shadow-lg h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl hover:from-purple-600 hover:to-purple-700 active:scale-95 hover:shadow-xl"
                >
                  .
                </button>
              </div>

              {/* Accept Button
              <button
                type="button"
                onClick={() => {
                  // Route to the same functionality as the original save button
                  if (
                    !selectedCustomer?.id ||
                    selectedCustomer?.name === "Cash Customer"
                  ) {
                    setShowConfirmation(true);
                  } else {
                    setShowMessagePreview(true);
                  }
                }}
                className="w-full h-16 mt-4 text-xl font-bold text-white transition-all duration-200 transform border-2 border-green-300 shadow-lg bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl hover:from-green-600 hover:to-emerald-700 active:scale-95 hover:shadow-xl"
              >
                ✓ Accept & Save
              </button> */}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="p-3 border-t border-gray-200">
          <div className="flex justify-end">
            <button
              ref={saveButtonRef}
              onClick={() => {
                if (
                  !selectedCustomer?.id ||
                  selectedCustomer?.name === "Cash Customer"
                ) {
                  setShowConfirmation(true);
                } else {
                  setShowMessagePreview(true);
                }
              }}
              className="px-5 py-3 text-xs font-medium text-white bg-indigo-600 rounded hover:bg-indigo-700"
            >
              {isUpdateMode ? "Update Sale" : "Save Bill"}
            </button>
          </div>
        </div>

        {/* Hidden Print Content */}
        <div className="hidden">
          <PrintView
            ref={printRef}
            printType="bill"
            products={products}
            totals={totals}
            customerInfo={selectedCustomer}
            companyDetails={companyDetails}
            receivedAmount={receivedAmount}
            balanceAmount={balanceAmount}
            paymentType={paymentType}
            billNumber={billNumber}
            billTime={billTime}
            template={defaultTemplate}
            renderDynamicTemplate={renderDynamicTemplate}
            formatCurrency={formatCurrency}
          />
        </div>

        {/* Message Preview Modal */}
        {showMessagePreview && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-md p-3 bg-white rounded shadow-xl">
              <h3 className="text-sm font-medium text-gray-900">
                Message Preview
              </h3>

              <div className="mt-2">
                <label className="block text-xs font-medium text-gray-700">
                  Message Content
                </label>
                <textarea
                  id="messageContent"
                  ref={messageContentRef}
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                  className="w-full h-20 p-1 mt-1 text-xs border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Edit your message here..."
                />
                <p className="mt-1 text-xs text-gray-600">
                  This message will be sent to{" "}
                  <span className="font-bold">
                    {selectedCustomer?.mobile || "N/A"}
                  </span>
                </p>
                {messageContent.includes("{{bill_url}}") && (
                  <div className="p-1 mt-1 rounded bg-blue-50">
                    <p className="mb-1 text-xs text-blue-600">Bill URL Preview:</p>
                    <a
                      href={`${window.location.origin}/api/bills/${billNumber}/view`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-500 underline break-all hover:text-blue-700"
                    >
                      {`${window.location.origin}/api/bills/${billNumber}/view`}
                    </a>
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-2 mt-3">
                <button
                  type="button"
                  onClick={() => setShowMessagePreview(false)}
                  className="px-2 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowMessagePreview(false);
                    setShowConfirmation(true);
                  }}
                  className="px-2 py-1 text-xs font-medium text-white bg-gray-600 rounded hover:bg-gray-700"
                >
                  Skip
                </button>
                <button
                  type="button"
                  onClick={handleSendMessage}
                  className="px-2 py-1 text-xs font-medium text-white bg-indigo-600 rounded hover:bg-indigo-700"
                >
                  Confirm & Send
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Confirmation Modal */}
        {/* {showConfirmation && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-md p-3 bg-white rounded shadow-xl">
              <h3 className="text-sm font-medium text-gray-900">Confirm Bill</h3>

              <div className="mt-2 text-xs">
                <p>
                  The bill total is{" "}
                  <span className="font-bold">
                    {formatCurrency(totals.finalTotal)}
                  </span>
                </p>
                <p className="mt-1">
                  <span className="font-medium">Balance:</span>{" "}
                  <span
                    className={
                      balanceAmount >= 0 ? "text-green-600" : "text-red-600"
                    }
                  >
                    {formatCurrency(balanceAmount)}
                  </span>
                </p>
                <p className="mt-2 font-medium">
                  Do you want to print the bill?
                </p>
              </div>

              <div className="flex justify-end gap-2 mt-3">
                <button
                  type="button"
                  onClick={() => setShowConfirmation(false)}
                  className="px-2 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  ref={saveOnlyButtonRef}
                  type="button"
                  onClick={handleConfirmSave}
                  className="px-2 py-1 text-xs font-medium text-white bg-gray-600 rounded hover:bg-gray-700"
                >
                  No, Just Save
                </button>
                <button
                  ref={printButtonRef}
                  type="button"
                  onClick={handleConfirmPrint}
                  className="px-2 py-1 text-xs font-medium text-white bg-indigo-600 rounded hover:bg-indigo-700"
                >
                  Yes, Print
                </button>
              </div>
            </div>
          </div>
        )} */}
        {/* Confirmation Modal */}
        {showConfirmation && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                Confirm Bill
              </h3>

              <div className="mt-4">
                <p className="text-sm text-gray-600">
                  The bill total is{" "}
                  <span className="font-bold">
                    {formatCurrency(totals.finalTotal)}
                  </span>
                </p>
                <p className="mt-2 text-sm">
                  <span className="font-medium">Balance:</span>{" "}
                  <span
                    className={
                      balanceAmount >= 0
                        ? "text-green-600 font-bold"
                        : "text-red-600 font-bold"
                    }
                  >
                    {formatCurrency(balanceAmount)}
                  </span>
                </p>
                <p className="mt-4 text-sm font-medium text-gray-700">
                  Do you want to print the bill?
                </p>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowConfirmation(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  ref={saveOnlyButtonRef}
                  type="button"
                  onClick={handleConfirmSave}
                  className="px-4 py-2 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  No, Just Save
                </button>
                <button
                  ref={printButtonRef}
                  type="button"
                  onClick={handleConfirmPrint}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Yes, Print
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="p-6 bg-white rounded-lg shadow-xl">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full">
                <svg
                  className="w-6 h-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div className="mt-3 text-center">
                <h3 className="text-lg font-medium leading-6 text-gray-900">
                  {isUpdateMode
                    ? "Sale Updated Successfully!"
                    : "Bill Saved Successfully!"}
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    {isUpdateMode
                      ? "The sale has been updated in the database."
                      : "The bill has been saved to the database."}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}


        {/* Error Message */}
        {error && (
          <div className="fixed px-2 py-1 text-xs text-red-700 bg-red-100 border border-red-400 rounded bottom-2 right-2">
            <p>{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TouchPOSBillModel;